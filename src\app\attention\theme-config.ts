// Theme configuration - Light theme only for remix flow
export const remixThemeConfig = {
  light: {
    // Primary brand colors for the remix contest (Pink)
    primary: {
      DEFAULT: "318, 98%, 74%", // #FE7CD6 - Exact Figma pink color
      foreground: "0 0% 100%", // White text on pink
    },
    // Secondary colors (Grays)
    secondary: {
      DEFAULT: "210 40% 98%", // #f9fafb - Light gray background
      foreground: "222.2 84% 4.9%", // Dark text on light gray
    },
    // Muted colors for subtle elements
    muted: {
      DEFAULT: "210 40% 96%", // #f3f4f6 - Very light gray
      foreground: "215.4 16.3% 46.9%", // #6b7280 - Medium gray text
    },
    // Accent colors (Orange for special elements)
    accent: {
      DEFAULT: "318, 98%, 74%", //   accent
      foreground: "0 0% 100%", // White text on orange
    },
    // Background and foreground
    background: "0 0% 100%", // Pure white
    foreground: "222.2 84% 4.9%", // Near black
    // UI elements
    card: {
      DEFAULT: "0 0% 100%", // White cards
      foreground: "222.2 84% 4.9%", // Dark text
    },
    border: "214.3 31.8% 91.4%", // #e5e7eb - Light border
    input: "214.3 31.8% 91.4%", // Same as border
    ring: "330 81% 67%", // Pink focus ring
    // Destructive (error) colors
    destructive: {
      DEFAULT: "0 84.2% 60.2%", // Red
      foreground: "0 0% 98%", // Light text
    },
  },
  dark: {
    // Primary brand colors for the remix contest (Pink) - adjusted for dark mode
    primary: {
      DEFAULT: "330 81% 67%", // Same pink, works well on dark
      foreground: "222.2 84% 4.9%", // Dark text on pink
    },
    // Secondary colors (Dark grays)
    secondary: {
      DEFAULT: "217.2 32.6% 17.5%", // #2d3748 - Dark gray background
      foreground: "210 40% 98%", // Light text on dark gray
    },
    // Muted colors for subtle elements
    muted: {
      DEFAULT: "217.2 32.6% 17.5%", // Same as secondary
      foreground: "215 20.2% 65.1%", // #9ca3af - Light gray text
    },
    // Accent colors (Orange for special elements)
    accent: {
      DEFAULT: "318, 98%, 74%", // Same pink
      foreground: "222.2 84% 4.9%", // Dark text on orange
    },
    // Background and foreground
    background: "222.2 84% 4.9%", // Dark background
    foreground: "210 40% 98%", // Light text
    // UI elements
    card: {
      DEFAULT: "222.2 84% 4.9%", // Dark cards
      foreground: "210 40% 98%", // Light text
    },
    border: "217.2 32.6% 17.5%", // Dark border
    input: "217.2 32.6% 17.5%", // Same as border
    ring: "330 81% 67%", // Pink focus ring
    // Destructive (error) colors
    destructive: {
      DEFAULT: "0 62.8% 30.6%", // Darker red for dark mode
      foreground: "210 40% 98%", // Light text
    },
  },
} as const;

export type RemixThemeConfig = typeof remixThemeConfig;
export type RemixThemeMode = keyof RemixThemeConfig;
