'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';
import { Play, Pause, Download, Send, LogOut, Loader2, Headphones, MicVocal, Disc3 } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { FileUploadModal } from '@/components/remix/file-upload-modal';
import { DisclaimerModal } from '@/components/remix/disclaimer-modal';
import { UploadFile } from '@/lib/file-upload-utils';
import { useMusicPlayer } from '@/contexts/music-player-context/music-player-context';
import { useAuth } from '@/contexts/auth/auth-context';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { RemixLoadingPage } from '@/components/remix/remix-loading-page';
import { useRemixFlow } from '@/lib/remix-flow-manager';

export default function RemixPage() {
  const router = useRouter();
  const { isAuthenticated, signOut } = useAuth();
  const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();
  
  // Use the new flow manager
  const {
    shouldShowLoading,
    shouldOpenUploadModal,
    userProfile,
  } = useRemixFlow();

  // Local state - ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  const [isAgreed, setIsAgreed] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDisclaimerModal, setShowDisclaimerModal] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [, setUploadedFiles] = useState<UploadFile[]>([]);

  // Handle automatic modal opening from flow manager
  React.useEffect(() => {
    if (shouldOpenUploadModal) {
      setShowUploadModal(true);
      // Clean up the URL by removing the query parameter
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [shouldOpenUploadModal]);

  // Show loading page when needed - AFTER ALL HOOKS ARE CALLED
  if (shouldShowLoading) {
    return <RemixLoadingPage />;
  }

  // Define the specific song for the remix contest
  const remixSong = {
    id: "remix-attention-kesha",
    title: "ATTENTION! (Open Verse)",
    artist: "Kesha",
    album: "Open Verse Contest",
    albumArt: "/remix/KESHAATTENTIONFinal_256.jpg",
    duration: 180,
    audioSrc: "https://d1jds31zoh6k0w.cloudfront.net/attention/ATTENTION!+Open+Verse.mp3",
    downloadSrc: "https://d1jds31zoh6k0w.cloudfront.net/attention/ATTENTION!+Open+Verse.wav",
    credits: {
      producer: "Kesha",
      writer: "Kesha",
      engineer: "Unknown",
    },
  };

  const handlePlay = () => {
    if (currentSong?.id === remixSong.id) {
      togglePlay();
    } else {
      playSong(remixSong, { disableNavigation: true, disableExpand: true });
    }
  };

  const handleDownload = async () => {
    setIsDownloading(true);

    try {
      const response = await fetch(remixSong.downloadSrc);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = 'ATTENTION_Open_Verse.wav';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setTimeout(() => URL.revokeObjectURL(blobUrl), 1000);

      toast.success('Download started successfully!', {
        position: 'top-right',
        className: 'bg-primary/10 text-primary',
      });
      setIsDownloading(false);
    } catch (error) {
      console.log('CORS error, opening in new tab:', error);
      window.open(remixSong.audioSrc, '_blank', 'noopener,noreferrer');

      toast('Opened in new tab for download', {
        description: 'If download does not start, please try again.',
        position: 'top-right',
        className: 'bg-primary/10 text-primary',
      });
      setIsDownloading(false);
    }
  };

  const handleSubmit = async () => {
    if (!isAgreed) return;

    // Check if user is authenticated
    if (!isAuthenticated) {
      router.push('/login?createApplication=true');
      return;
    }

    // Check if user has completed remix onboarding
    if (!userProfile?.isOnboarded) {
      router.push('/attention/onboarding?createApplication=true');
    } else {
      // User is authenticated and onboarded, show submit modal immediately
      setShowUploadModal(true);
    }
  };

  const handleUploadComplete = (files: UploadFile[]) => {
    setUploadedFiles(files);
    console.log('Files uploaded:', files);
  };

  const handleCloseModal = () => {
    setShowUploadModal(false);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/attention');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };
  console.log(userProfile)

  console.log(userProfile?.profileImage)

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Fixed Header */}
      <header className="sticky top-0 z-50 bg-background flex items-center justify-between px-4 md:px-8 lg:px-16 py-4 border-b border-border">
        <div className="flex items-end gap-1">
          <div className="w-28 h-5 relative">
            <Image src="/SMASH-(full)-logo.png" alt="SMASH" fill className="object-contain" />
          </div>
        </div> 
        <div className="flex items-center gap-4">
          {/* User Profile Section - Show when authenticated and has profile */}
          {isAuthenticated && userProfile && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="w-8 h-8 rounded-full overflow-hidden bg-muted flex items-center justify-center cursor-pointer border-1 border-primary shadow-sm hover:shadow-lg transition-shadow duration-200">
                  {userProfile.profileImage ? (
                    <Image 
                      src={userProfile.profileImage } 
                      alt={userProfile.name || userProfile.artistName || 'User'} 
                      width={32} 
                      height={32} 
                      className="object-cover"
                    />
                  ) : (
                    <div
                      className="w-10 h-10 bg-primary/15 rounded-full outline outline-1 outline-offset-[-0.5px] outline-primary inline-flex justify-center items-center gap-2.5"
                    >
                      <Image
                        src="/remix/smash-user.svg"
                        alt="User"
                        width={20}
                        height={20}
                        className="object-cover"
                      />
                    </div>
                  )}
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-52 rounded-xl bg-background shadow-lg p-2 mt-2">
                <div className="px-3 py-2 border-b border-border mb-2 flex items-center gap-2">
                  {userProfile.profileImage ? (
                     <div
                      className="w-10 h-10 bg-primary/15 rounded-full outline outline-1 outline-offset-[-0.5px] outline-primary inline-flex justify-center items-center gap-2.5"
                    >
                    <Image
                      src={userProfile.profileImage}
                      alt={userProfile.name || userProfile.artistName || 'User'}
                      width={20}
                      height={20}
                      className="rounded-full object-cover border border-primary"
                      />
                      </div>
                  ) : (
                    <div className="w-7 h-7 bg-primary/15 rounded-full flex items-center justify-center">
                      <Image src="/remix/smash-user.svg" alt="User" width={16} height={16} />
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-semibold text-foreground capitalize leading-tight">
                      {userProfile.name || userProfile.artistName || userProfile.email}
                    </p>
                    {userProfile.artistName && userProfile.name !== userProfile.artistName && (
                      <p className="text-xs text-muted-foreground leading-tight">
                        {userProfile.artistName}
                      </p>
                    )}
                  </div>
                </div>
                <DropdownMenuItem onClick={handleLogout} className="text-destructive focus:text-destructive font-semibold rounded-lg px-3 py-2 hover:bg-destructive/10 transition-colors flex items-center gap-2">
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}


          {/* Settings Dropdown */}
          {!isAuthenticated  &&(
            <Button
              onClick={() => {
                router.push('/login');
              }}
              className="px-6 py-2 bg-primary text-primary-foreground rounded-full font-bold text-base font-arvo shadow hover:bg-primary/90 transition-colors focus:outline-none focus:ring-2 focus:ring-primary"
            >
              Login
            </Button>
          )}
        </div>
      </header>

      {/* Scrollable Main Content */}
      <main className="flex-1 overflow-y-auto bg-background relative">
        {/* Content Container */}
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-10 space-y-6 lg:space-y-8">

          {/* Hero Section */}
          <div className="text-center space-y-1 lg:space-y-2 justify-items-center">
            {/* Join Badge */}
            <div className="inline-block bg-[#FFD2F1] rounded-md px-3 py-1 lg:px-4 lg:py-1">
              <span className="text-foreground font-semibold text-xl lg:text-2xl">Join</span>
            </div>

            {/* Contest Title */}
            <h2 className="text-3xl lg:text-5xl font-bold text-foreground leading-tight">
  ATTENTION! Competiton
</h2>



            {/* Artist Info */}
            <div className="flex items-center justify-center gap-2 lg:gap-3">
              <div className="w-8 h-8 lg:w-9 lg:h-9 rounded-full overflow-hidden">
                <Image src="/remix/kesha-avatar.png" alt="Kesha" width={36} height={36} className="object-cover" />
              </div>
              <span className="text-muted-foreground font-normal text-lg lg:text-xl">By Kesha</span>
            </div>
          

          {/* Main Hero Image with Pink Circle */}
          <div className="flex justify-center items-center w-full my-4">
            <Image
              src="/remix/main-image.png"
              alt="Contest Image"
              height={500}
              width={500}
              className="object-cover rounded-lg lg:mb-0"
            />
          </div>

          {/* Large ATTENTION Title */}
          <div className="w-full flex justify-center lg:px-0">
            <h1 className="text-[60px] lg:text-[80px] font-bold text-primary leading-[1.5] lg:leading-[2] mb-0 lg:mt-[-2rem] text-center">
              ATTENTION!
            </h1>
          </div>

          {/* Action Buttons */}
          <div className="w-full flex justify-center lg:px-0 lg:mt-[-2rem]">
            <div className="flex flex-col lg:flex-row justify-center items-center gap-3 lg:gap-4 w-[480px] sm:w-[520px] lg:w-[520px]">
              <Button
                onClick={handlePlay}
                size="lg"
                className="bg-transparent text-primary cursor-pointer w-full lg:w-[120px] border border-primary rounded-full px-8 py-6 font-bold uppercase hover:bg-primary/5 text-base"
              >
                {currentSong?.id === remixSong.id && isPlaying ? (
                  <>
                    <Pause className="w-4 h-4" />
                    PAUSE
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    PLAY
                  </>
                )}
              </Button>
              <Button
                onClick={handleDownload}
                disabled={isDownloading}
                size="lg"
                className="bg-transparent text-primary cursor-pointer w-full lg:w-[380px] border border-primary rounded-full px-8 py-6 font-bold uppercase hover:bg-primary/5 text-base"
              >
                {isDownloading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Download className="w-4 h-4" />
                )}
                <span className="hidden sm:inline">
                  {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD OPEN VERSE'}
                </span>
                <span className="sm:hidden">
                  {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD'}
                </span>
              </Button>
            </div>
          </div>
         
         </div>

          {/* Content Sections */}
          <div className="space-y-3 lg:space-y-4">

            {/* Kesha's Message Section */}
        <section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
  <h3 className="text-lg lg:text-[22px] font-bold text-card-foreground mb-2 lg:mb-3">
    Kesha&apos;s Message
  </h3>
  <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 space-y-3 lg:space-y-4">
    <h4 className="text-lg lg:text-2xl font-bold leading-normal text-primary">
      Do I have your ATTENTION!?
    </h4>
    <p className="font-secondary">It&apos;s official! I&apos;ve created another banger with my new song ATTENTION! featuring Slayyyter, and Rose Gray.</p>
    <p className="font-secondary">And now it&apos;s time to pass the mic and bring ATTENTION! TO YOU: <br />YOUR VOICE, <br />YOUR SCENE, <br />YOUR MESSAGE</p>
    <p className="font-secondary">That&apos;s right I&apos;m sharing a version of ATTENTION! with an open verse so you can give this song your own twist and bring ATTENTION! to whatever you&apos;re passionate about. Go wild and let&apos;s make a SMASH together. Music knows no borders so I&apos;m excited to hear what you cook up from all over the world!</p>
    <p className="font-secondary">I&apos;m going to make sure the Artists behind the winning features get proper credit, royalties, and get paid! After all that&apos;s the whole point of my new music creation community SMASH. Keep in mind at SMASH and Kesha Records we nurture a safe and supportive community where all creators feel safe – so let&apos;s keep vibes positive!</p>
    <p className="font-secondary">Together we will remake a more fair music business… one SMASH at a time!</p>
    <p className="font-secondary">– Kesha</p>
  </div>
</section>


          {/* How to Participate Section */}
<section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
  <h3 className="text-lg lg:text-[22px] font-bold text-primary uppercase mb-3 lg:mb-4">HOW TO PARTICIPATE</h3>
  <div className="space-y-2">

    {/* Step 1: Sign into SMASH */}
    <div className="flex gap-3 lg:gap-4">
      <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
        <Image src="/remix/smash-user.svg" alt="" height={30} width={24} className="object-cover" />
        <div className="w-0.5 h-6 lg:h-8 bg-primary mt-2"></div>
      </div>
      <div className="flex-1 pt-0.5">
        <h4 className="text-sm lg:text-base font-bold text-card-foreground mb-1">Sign into SMASH</h4>
        <p className="text-muted-foreground text-sm lg:text-base font-secondary leading-5 lg:leading-6">Join the community of music creators</p>
      </div>
    </div>

    {/* Step 2: Listen to ATTENTION! */}
    <div className="flex gap-3 lg:gap-4">
      <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
        <div className="w-0.5 h-2 bg-primary"></div>
        <Headphones className="w-5 h-5 lg:w-6 lg:h-6 " />
        <div className="w-0.5 h-6 lg:h-8 bg-primary mt-2"></div>
      </div>
      <div className="flex-1 pt-0.5">
        <h4 className="text-sm lg:text-base font-bold text-card-foreground mb-1">Listen to ATTENTION!</h4>
        <p className="text-muted-foreground text-sm lg:text-base font-secondary leading-5 lg:leading-6">Download a version of ATTENTION! with an open verse</p>
      </div>
    </div>

    {/* Step 3: Sing Your Heart Out */}
    <div className="flex gap-3 lg:gap-4">
      <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
        <div className="w-0.5 h-2 bg-primary"></div>
        <MicVocal className="w-5 h-5 lg:w-6 lg:h-6 " />
        <div className="w-0.5 h-6 lg:h-8 bg-primary mt-2"></div>
      </div>
      <div className="flex-1 pt-0.5">
        <h4 className="text-sm lg:text-base font-bold text-card-foreground mb-1">Sing Your Heart Out</h4>
        <p className="text-muted-foreground text-sm lg:text-base font-secondary leading-5 lg:leading-6">SMASH It! Record your own unique verse on ATTENTION! - don&apos;t hold back because only a few winners will be chosen</p>
      </div>
    </div>

    {/* Step 4: Submit Your SMASH */}
    <div className="flex gap-3 lg:gap-4">
      <div className="flex flex-col items-center flex-shrink-0 w-6 lg:w-8">
        <div className="w-0.5 h-2 bg-primary"></div>
        <Disc3 className="w-5 h-5 lg:w-6 lg:h-6 " />
      </div>
      <div className="flex-1 pt-0.5">
        <h4 className="text-sm lg:text-base font-bold text-card-foreground mb-1">Submit Your SMASH</h4>
        <p className="text-muted-foreground text-sm lg:text-base font-secondary leading-5 lg:leading-6">Upload your recording and share a video on socials tagging SMASH and Kesha to let us know you submitted</p>
      </div>
    </div>
  </div>
</section>

{/* Prize Section */}
<section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
  <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6  space-y-3 lg:space-y-4">
    <h4 className="font-bold text-primary text-base lg:text-lg">PRIZE</h4>
    <p className="font-secondary">Kesha will choose a few of the feature submissions to release officially via Kesha Records with the featured artist prominently credited. She will share the new composition across all DSPs as well as Kesha&apos;s own socials and the socials of SMASH and Kesha Records giving your voice the ATTENTION! it deserves, introducing the featured artist to a wide audience and gaining valuable exposure.</p>
    <p className="font-secondary">Featured artist will get 20% share of the publishing and 20% share of the net artist royalties from the master side plus a $2,000 cash fee.</p>
  </div>
</section>

{/* Deadline Section */}
<section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
  <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 font-inter space-y-3 lg:space-y-4">
    <h4 className="font-bold text-primary text-base lg:text-lg">DEADLINE</h4>
    <p className="font-secondary">All features must be uploaded to SMASH by August 25th at 11:59 PT.</p>
  </div>
</section>

{/* Submission Guidelines Section */}
<section className="bg-card rounded-lg px-3 lg:px-4 py-4 lg:py-5">
  <div className="space-y-4 lg:space-y-5">
    <div className="text-card-foreground text-sm lg:text-base leading-5 lg:leading-6 font-inter space-y-3 lg:space-y-4">
      <h4 className="font-bold text-primary text-base lg:text-lg">SUBMISSION GUIDELINES</h4>
      <p className="font-secondary">By submitting your original work in the correct format and on time, you agree to follow all SMASH Music contest rules and allow use of your submission for promotion. Only one entry per participant is allowed. Non-compliance may result in disqualification.</p>
    </div>

    {/* Checkbox */}
    <div className="flex items-start gap-2 lg:gap-3 pt-2 lg:pt-3">
      <Checkbox
        id="terms-agreement"
        checked={isAgreed}
        onCheckedChange={(checked) => setIsAgreed(checked as boolean)}
        className="mt-0.5 lg:mt-1 w-4 h-4 lg:w-5 lg:h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
      />
      <label htmlFor="terms-agreement" className="text-sm lg:text-base text-card-foreground cursor-pointer font-normal leading-5 lg:leading-6 font-secondary">
        I have read and agree to the{' '}
        <button
          type="button"
          onClick={() => setShowDisclaimerModal(true)}
          className="text-card-foreground font-extrabold hover:text-primary/80 "
        >
          Terms & Conditions
        </button>{' '}
        of the contest.
      </label>
    </div>
  </div>
</section>


            {/* Bottom Action Buttons */}
            <section className="text-center pt-4 lg:pt-6 px-4 lg:px-0">
              <div className="flex flex-col gap-4 w-full max-w-2xl mx-auto">
                <div className="flex flex-col lg:flex-row justify-center items-center gap-3 lg:gap-4 w-full">
                  <Button
                    onClick={handleDownload}
                    disabled={isDownloading}
                    size="lg"
                    className="bg-transparent text-primary cursor-pointer w-full lg:flex-1 border border-primary rounded-full px-8 lg:px-12 py-6 font-bold uppercase hover:bg-primary/5 min-w-0"
                  >
                    {isDownloading ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Download className="w-4 h-4" />
                    )}
                    <span className="hidden sm:inline">
                      {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD OPEN VERSE'}
                    </span>
                    <span className="sm:hidden">
                      {isDownloading ? 'DOWNLOADING...' : 'DOWNLOAD'}
                    </span>
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={!isAgreed}
                    size="lg"
                    className="bg-primary text-primary-foreground cursor-pointer w-full lg:flex-1 border border-primary rounded-full px-8 lg:px-12 py-6 font-bold uppercase hover:bg-primary/90 min-w-0"
                  >
                    <Send className="w-4 h-4" />
                    SUBMIT TRACK
                  </Button>
                </div>
                <p className="font-secondary px-2 py-2 text-base text-muted-foreground bg-background rounded-lg w-full max-w-2xl mx-auto">
                  If you have any questions, please email us at <span className="text-primary"><EMAIL></span>
                </p>
              </div>
            </section>
          </div>

          {/* Footer */}
          <footer className="text-center py-6 mt-8 gap-2">
            
            <p className="text-foreground text-xl font-semibold font-lato">By SMASH Music</p>
          </footer>
        </div>

        <div className="absolute inset-0 pointer-events-none overflow-hidden" aria-hidden="true">
          {/* Desktop background images */}
          <div className="hidden lg:block">
            {/* Element 1: Top left area */}
            <div className="absolute top-[180px] left-[120px] w-[134px] h-[153px]">
              <Image src="/remix/bg-image-1-3ba70c.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 2: Top right area */}
            <div className="absolute top-[160px] right-[120px] w-[103px] h-[114px]">
              <Image src="/remix/bg-image-2-24c5d3.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 3: Left middle */}
            <div className="absolute top-[480px] left-[180px] w-[139px] h-[159px]">
              <Image src="/remix/bg-image-3-645d1a.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 4: Bottom right */}
            <div className="absolute top-[665px] right-[180px] w-[131px] h-[91px] md:right-[90px]">
              <Image src="/remix/bg-image-4-41e4b7.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 5: Right middle */}
            <div className="absolute top-[320px] right-[250px] w-[112px] h-[96px]">
              <Image src="/remix/bg-image-5-786eb3.png" alt="" fill className="object-cover" />
            </div>
            <div className="absolute top-[330px] right-[-15px] ">
              <Image src="/remix/bg-image-8-13131d.png" alt="" height={165} width={105} className="object-cover" />
            </div>
            {/* Element 6: Far left */}
            <div className="absolute top-[400px] left-[-40px] w-[165px] h-[105px]">
              <Image src="/remix/bg-image-7-13131d.png" alt="" fill className="object-cover" />
            </div>
          </div>

          {/* Tablet/iPad background images */}
          <div className="hidden md:block lg:hidden">
            {/* Element 1: Top left */}
            <div className="absolute top-[120px] left-[60px] w-[100px] h-[110px]">
              <Image src="/remix/bg-image-1-3ba70c.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 2: Top right */}
            <div className="absolute top-[110px] right-[60px] w-[80px] h-[90px]">
              <Image src="/remix/bg-image-2-24c5d3.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 3: Left middle */}
            <div className="absolute top-[500px] left-[80px] w-[90px] h-[100px]">
              <Image src="/remix/bg-image-3-645d1a.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 4: Bottom right */}
            <div className="absolute top-[500px] right-[80px] w-[90px] h-[60px]">
              <Image src="/remix/bg-image-4-41e4b7.png" alt="" fill className="object-cover" />
            </div>
            {/* Element 5: Right middle */}
            <div className="absolute top-[250px] right-[80px] w-[80px] h-[70px]">
              <Image src="/remix/bg-image-5-786eb3.png" alt="" fill className="object-cover" />
            </div>
            <div className="absolute top-[300px] right-[10px] w-[70px] h-[110px]">
              <Image src="/remix/bg-image-8-13131d.png" alt="" height={110} width={70} className="object-cover" />
            </div>
            {/* Element 6: Far left */}
            <div className="absolute top-[300] left-[0px] w-[100px] h-[70px]">
              <Image src="/remix/bg-image-7-13131d.png" alt="" fill className="object-cover" />
            </div>
          </div>

          {/* Mobile background images - positioned as per mobile Figma */}
          <div className="block md:hidden">
            {/* Mobile Element 1: Top left */}
            <div className="absolute top-[125px] left-[42px] w-[66px] h-[76px]">
              <Image src="/remix/bg-image-1-3ba70c.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 2: Top right */}
            <div className="absolute top-[160px] right-[27px] w-[49px] h-[54px]">
              <Image src="/remix/bg-image-2-24c5d3.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 3: Left middle */}
            <div className="absolute top-[335px] left-[1px] w-[60px] h-[65px]">
              <Image src="/remix/bg-image-3-645d1a.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 4: Right middle */}
            <div className="absolute top-[375px] right-[1px] w-[54px] h-[46px]">
              <Image src="/remix/bg-image-5-786eb3.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 5: Bottom left */}
            <div className="absolute top-[240px] left-[-24px] w-[82px] h-[52px]">
              <Image src="/remix/bg-image-7-13131d.png" alt="" fill className="object-cover" />
            </div>
            {/* Mobile Element 6: Bottom right */}
            <div className="absolute top-[265px] right-[-24px] w-[75px] h-[85px]">
              <Image src="/remix/bg-image-4-41e4b7.png" alt="" fill className="object-cover" />
            </div>
          </div>
        </div>
      </main>

      {/* File Upload Modal */}
      <FileUploadModal
        isOpen={showUploadModal}
        onClose={handleCloseModal}
        onSubmit={handleUploadComplete}
      />

      {/* Disclaimer Modal */}
      <DisclaimerModal
        isOpen={showDisclaimerModal}
        onOpenChange={setShowDisclaimerModal}
      />

      <Toaster
        position="top-right"
        toastOptions={{
          classNames: {
            toast: 'bg-primary/10 text-primary',
            description: 'text-primary',
            actionButton: 'bg-primary text-primary-foreground',
            cancelButton: 'bg-muted',
          },
        }}
      />
    </div>
  );
}